{"format": 1, "restore": {"D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Application\\SMMS.Application.csproj": {}}, "projects": {"D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Application\\SMMS.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Application\\SMMS.Application.csproj", "projectName": "SMMS.Application", "projectPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Application\\SMMS.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\Users\\<USER>\\.nuget\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj": {"projectPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj"}, "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj": {"projectPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ClosedXML": {"target": "Package", "version": "[0.105.0, )"}, "CloudinaryDotNet": {"target": "Package", "version": "[1.27.5, )"}, "EPPlus": {"target": "Package", "version": "[8.0.5, )"}, "FirebaseAdmin": {"target": "Package", "version": "[3.2.0, )"}, "MailKit": {"target": "Package", "version": "[4.13.0, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.41, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj", "projectName": "SMMS.Domain", "projectPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\Users\\<USER>\\.nuget\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj", "projectName": "SMMS.Infrastructure", "projectPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\Users\\<USER>\\.nuget\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj": {"projectPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev3\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204/PortableRuntimeIdentifierGraph.json"}}}}}