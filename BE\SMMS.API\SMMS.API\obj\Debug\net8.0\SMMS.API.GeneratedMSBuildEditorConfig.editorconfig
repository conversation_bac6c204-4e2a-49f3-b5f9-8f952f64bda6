is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = SMMS.API
build_property.RootNamespace = SMMS.API
build_property.ProjectDir = D:\FPT\SE8\SWP\SWP391_SMMS_Dev3\BE\SMMS.API\SMMS.API\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\FPT\SE8\SWP\SWP391_SMMS_Dev3\BE\SMMS.API\SMMS.API
build_property._RazorSourceGeneratorDebug = 
