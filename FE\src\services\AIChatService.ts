import supabase from "@/utils/supabase";

export async function AIChatService(message: string, sessionId: string, userId: string) {
    try {
        const contextLimit = 20;
        let context = [];

        const { data: chatMessages, error: fetchError } = await supabase
            .from('Message')
            .select('mess, role_chat')
            .eq('session_id', sessionId)
            .eq('user_id', userId)
            .order('created_at', { ascending: true })
            .limit(contextLimit * 2);

        if (fetchError) {
            console.error("Fetch messages error:", fetchError);
            return null;
        }

        if (!chatMessages || chatMessages.length === 0) {
            context.push({
                role: "user",
                parts: [{
                    text: "Bạn là chuyên gia tư vấn chăm sóc sức khỏe chuyên về sức khỏe trẻ em. Tôi cần lời khuyên chuyên môn của bạn về việc xây dựng một hệ thống AI hướng dẫn phụ huynh về sức khỏe của con em mình, bao gồm dinh dưỡng, sức khỏe tâm thần và chăm sóc phòng ngừa. Vui lòng đề xuất các lĩnh vực trọng tâm và chiến lược tương tác. Trả lời bằng tiếng Việt."
                }]
            });
        }

        context = context.concat(
            (chatMessages ?? []).map(msg => ({
                role: msg.role_chat === "user" ? "user" : "model",
                parts: [{ text: msg.mess }]
            }))
        );

        context.push({
            role: "user",
            parts: [{ text: message }]
        });

        const response = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", {
            method: "POST",
            headers: {
                "X-goog-api-key": "AIzaSyDoK9UmKmYREJmgLvukWG0D2U7IwBN535E",
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ contents: context })
        });

        const aiData = await response.json();
        const aiMessage = aiData?.candidates?.[0]?.content?.parts?.[0]?.text || "(No reply)";

        const now = new Date().toISOString();

        const { error: userInsertError } = await supabase
            .from('Message')
            .insert([{
                mess: message,
                role_chat: "user",
                user_id: userId,
                session_id: sessionId,
                created_at: now
            }]);

        if (userInsertError) {
            console.error("Lỗi khi lưu message user:", userInsertError);
        }

        const { error: aiInsertError } = await supabase
            .from('Message')
            .insert([{
                mess: aiMessage,
                role_chat: "ai",
                user_id: "model",
                session_id: sessionId,
                created_at: now
            }]);

        if (aiInsertError) {
            console.error("Lỗi khi lưu message AI:", aiInsertError);
        }

        return aiMessage;

    } catch (error) {
        console.error("AIChatService error:", error);
        return null;
    }
}
